#!/usr/bin/env python3
"""
Django Management Command: Fix Hadis Translation Language Code
==============================================================

This command finds Hadis ID 7079 and checks its translations.
If there are 2 translation objects and one has language code 'en',
it changes that language code to 'tg' (Tajik).

Usage:
    python manage.py fix_hadis_translation_language --test    # Test mode (no changes)
    python manage.py fix_hadis_translation_language --apply   # Apply changes

Author: Generated for Habib Backend - Hadis Translation Fix
Date: 2025-07-28
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.hadis.models import Hadis
from dj_language.models import Language
import json


class Command(BaseCommand):
    help = 'Fix Hadis translations (ID 7079-8157) language code from EN to TG'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test',
            action='store_true',
            help='Run in test mode (no database changes)',
        )
        parser.add_argument(
            '--apply',
            action='store_true',
            help='Apply changes to database',
        )

    def handle(self, *args, **options):
        """Main command handler"""
        
        # Check arguments
        if not options['test'] and not options['apply']:
            raise CommandError('You must specify either --test or --apply')
        
        if options['test'] and options['apply']:
            raise CommandError('Cannot use both --test and --apply at the same time')
        
        test_mode = options['test']
        
        self.stdout.write(
            self.style.SUCCESS('🔍 Hadis Translation Language Code Fixer (ID 7079-8157)')
        )
        self.stdout.write('=' * 70)

        if test_mode:
            self.stdout.write(
                self.style.WARNING('⚠️  RUNNING IN TEST MODE - NO CHANGES WILL BE MADE')
            )
        else:
            self.stdout.write(
                self.style.ERROR('🚨 RUNNING IN APPLY MODE - CHANGES WILL BE MADE')
            )

        self.stdout.write('=' * 70)

        # Find all Hadis from ID 7079 to 8157
        hadis_range = list(range(7079, 8158))  # 8158 to include 8157
        hadis_queryset = Hadis.objects.filter(id__in=hadis_range).order_by('id')

        total_hadis = hadis_queryset.count()
        self.stdout.write(f"📊 Found {total_hadis} hadis in range 7079-8157")

        if total_hadis == 0:
            raise CommandError('❌ No hadis found in the specified range')
        
        # Check if TG language exists
        try:
            tg_language = Language.objects.get(code='tg')
            self.stdout.write(f"✅ Found TG language: {tg_language.name} ({tg_language.code})")
        except Language.DoesNotExist:
            raise CommandError("❌ Language with code 'tg' not found in Language model")

        # Process each hadis
        problems_found = 0
        changes_made = 0

        for hadis in hadis_queryset:
            self.stdout.write(f"\n🔍 Processing Hadis ID: {hadis.id}")

            # Check if hadis has exactly 2 translations
            if len(hadis.translations) != 2:
                self.stdout.write(f"   ⚠️  Skipping - Expected 2 translations, found {len(hadis.translations)}")
                continue

            # Find EN translation
            en_translation_index = None
            en_translation = None

            for i, trans in enumerate(hadis.translations):
                if trans.get('language_code') == 'en':
                    en_translation_index = i
                    en_translation = trans
                    break

            if en_translation_index is None:
                self.stdout.write("   ⚠️  Skipping - No translation with language code 'en' found")
                continue

            # Check if the EN translation text looks like Tajik (contains Cyrillic)
            en_text = en_translation.get('text', '')
            has_cyrillic = any('\u0400' <= char <= '\u04FF' for char in en_text)

            if not has_cyrillic:
                self.stdout.write("   ⚠️  Skipping - EN translation doesn't contain Cyrillic (not Tajik)")
                continue

            # Found a problem!
            problems_found += 1
            self.stdout.write(f"   🚨 PROBLEM FOUND:")
            self.stdout.write(f"      Language Code: {en_translation.get('language_code')}")
            self.stdout.write(f"      Text (first 80 chars): {en_text[:80]}...")
            self.stdout.write(f"      Contains Cyrillic: {has_cyrillic}")

            if test_mode:
                self.stdout.write(f"   ✅ TEST MODE: Would change 'en' → 'tg'")
                changes_made += 1
            else:
                # Apply the change
                try:
                    with transaction.atomic():
                        # Create a copy of translations to modify
                        updated_translations = hadis.translations.copy()

                        # Update the EN translation to TG
                        updated_translations[en_translation_index]['language_code'] = 'tg'

                        # Save the updated translations
                        hadis.translations = updated_translations
                        hadis.save()

                        changes_made += 1
                        self.stdout.write(f"   ✅ FIXED: Changed 'en' → 'tg'")

                except Exception as e:
                    self.stdout.write(f"   ❌ ERROR: {str(e)}")

        # Summary
        self.stdout.write("\n" + "=" * 70)
        self.stdout.write(f"📊 SUMMARY:")
        self.stdout.write(f"   Total hadis processed: {total_hadis}")
        self.stdout.write(f"   Problems found: {problems_found}")

        if test_mode:
            self.stdout.write(f"   Would be fixed: {changes_made}")
            self.stdout.write(
                self.style.SUCCESS("✅ TEST MODE: No actual database changes were made")
            )
        else:
            self.stdout.write(f"   Successfully fixed: {changes_made}")
            self.stdout.write(
                self.style.SUCCESS("✅ APPLY MODE: Changes have been applied to database")
            )

        self.stdout.write("\n" + "=" * 70)
        self.stdout.write(
            self.style.SUCCESS("🎉 Command completed successfully!")
        )
