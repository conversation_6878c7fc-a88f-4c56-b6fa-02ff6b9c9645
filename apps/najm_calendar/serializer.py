from rest_framework import serializers

from .models import CalendarOccasions, OccasionDetail


class CalendarSerializer(serializers.ModelSerializer):
    type = serializers.CharField(source='occasion_type')
    title = serializers.SerializerMethodField()
    countries = serializers.SerializerMethodField()
    holiday_in_countries = serializers.SerializerMethodField()
    dates = serializers.SerializerMethodField()
    has_occasion_detail = serializers.SerializerMethodField()  


    def get_countries(self, obj):
        if not obj.countries or obj.countries[0] == 'ALL':
            return ["All"]

        return [country.name or country.code for country in obj.countries]

    def get_holiday_in_countries(self, obj):
        return [country.name or country.code for country in obj.holiday_in_countries]

    def get_dates(self, obj):
        dates = []
        for date in obj.dates:
            dates.append({
                'day': str(date['day']),
                'month': str(date['month']),
                'year': str(date.get('year', '')),
            })
        return dates

    def get_title(self, obj):
        request = self.context.get('request')
        language_code = request.GET.get('language_code', request.LANGUAGE_CODE)

        for i in obj.translations:
            if i['language_code'] == language_code:
                return i['title']

        return None

    def get_has_occasion_detail(self, obj):
        request = self.context.get('request')
        language_code = request.GET.get('language_code', request.LANGUAGE_CODE)

        return OccasionDetail.objects.filter(
            occasion=obj, 
            language__code=language_code
        ).exists()
    
    class Meta:
        model = CalendarOccasions
        fields = ('id', 'title', 'countries', 'type', 'event_type', 'holiday_in_countries', 'dates', 'is_yearly', 'has_occasion_detail')

class OccasionDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = OccasionDetail
        fields = ['id', 'occasion', 'content',]
