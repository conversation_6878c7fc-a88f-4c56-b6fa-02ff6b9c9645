"""
تنظیمات ماژول ترجمه کلمه به کلمه قرآن
"""

import os
from typing import List


class TranslationConfig:
    """کلاس تنظیمات ترجمه"""
    
    # API Configuration
    GEMINI_MODEL = "gemini-2.0-flash-lite"
    API_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models"
    
    # Default API Keys (can be overridden)
    DEFAULT_API_KEYS = [
        "AIzaSyBz8OtW_zJPRiDA4YxTOBxaw2sv3oU6gwY",
        "AIzaSyBJv9CfDRbCJu4uvFXUHEKNSSTFO47IebA",
        "AIzaSyDXdorZ9JrLYkhUJhb-LwXD9e_S-nVD_0w",
        "AIzaSyD7zMokI5CYuN3SDAGxo7s5zQbbnD6qIL8",
        "AIzaSyBZ7t5NhJl2XiCv6EuuzHxTJmtJyCZvfqA",
    ]
    
    # Processing Configuration
    BATCH_SIZE = 25  # تعداد کلمات در هر درخواست (افزایش از 10 به 25)
    MAX_RETRIES = 3  # حداکثر تلاش مجدد
    RETRY_DELAY = 2  # تأخیر بین تلاش‌ها (ثانیه)
    REQUEST_TIMEOUT = 90  # timeout درخواست (ثانیه) - افزایش برای پردازش بیشتر
    BATCH_DELAY = 2  # تأخیر بین دسته‌ها (ثانیه) - کاهش برای سرعت بیشتر
    
    # File Paths (relative to translation module)
    MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
    DATA_DIR = os.path.join(MODULE_DIR, "data")
    
    # Input/Output Files
    INPUT_FILE = "apps/quran/management/commands/main.json"
    OUTPUT_FILE = os.path.join(DATA_DIR, "main_az.json")
    PROGRESS_FILE = os.path.join(DATA_DIR, "translation_progress.json")
    REQUESTS_LOG_FILE = os.path.join(DATA_DIR, "translation_requests.json")
    
    # Generation Configuration
    GENERATION_CONFIG = {
        "temperature": 0.1,
        "topK": 1,
        "topP": 1,
        "maxOutputTokens": 4096,
    }
    
    # Test Mode Configuration
    TEST_MODE = False  # حالت تست (بدون تغییر دیتابیس)
    TEST_BATCH_SIZE = 10  # اندازه دسته در حالت تست (افزایش از 3 به 10)
    TEST_MAX_WORDS = 50  # حداکثر کلمات در حالت تست (افزایش از 20 به 50)
    
    @classmethod
    def ensure_data_dir(cls):
        """اطمینان از وجود دایرکتوری داده‌ها"""
        os.makedirs(cls.DATA_DIR, exist_ok=True)
    
    @classmethod
    def get_api_url(cls, api_key: str) -> str:
        """دریافت URL کامل API"""
        return f"{cls.API_BASE_URL}/{cls.GEMINI_MODEL}:generateContent?key={api_key}"
    
    @classmethod
    def set_test_mode(cls, enabled: bool = True):
        """فعال/غیرفعال کردن حالت تست"""
        cls.TEST_MODE = enabled
        if enabled:
            # در حالت تست، فایل‌ها را در دایرکتوری test قرار می‌دهیم
            test_dir = os.path.join(cls.DATA_DIR, "test")
            os.makedirs(test_dir, exist_ok=True)
            
            cls.OUTPUT_FILE = os.path.join(test_dir, "main_az_test.json")
            cls.PROGRESS_FILE = os.path.join(test_dir, "translation_progress_test.json")
            cls.REQUESTS_LOG_FILE = os.path.join(test_dir, "translation_requests_test.json")
    
    @classmethod
    def get_config_summary(cls) -> dict:
        """خلاصه تنظیمات"""
        return {
            "model": cls.GEMINI_MODEL,
            "batch_size": cls.BATCH_SIZE,
            "max_retries": cls.MAX_RETRIES,
            "test_mode": cls.TEST_MODE,
            "api_keys_count": len(cls.DEFAULT_API_KEYS),
            "data_dir": cls.DATA_DIR,
            "output_file": cls.OUTPUT_FILE,
        }
