"""
پرامپت‌های بهینه شده برای ترجمه کلمات قرآن
"""

from typing import List, Dict


class QuranTranslationPrompts:
    """کلاس پرامپت‌های ترجمه قرآن"""
    
    @staticmethod
    def build_translation_prompt(words_batch: List[Dict], verse_text: str, target_language: str = "آذربایجانی") -> str:
        """
        ساخت پرامپت بهینه شده برای ترجمه کلمات قرآن
        
        Args:
            words_batch: لیست کلمات برای ترجمه
            verse_text: متن کامل آیه
            target_language: زبان مقصد
            
        Returns:
            پرامپت بهینه شده
        """
        
        # ساخت لیست کلمات با شماره‌گذاری
        words_list = []
        for i, word_data in enumerate(words_batch, 1):
            arabic_word = word_data['arabic'].strip()
            english_word = word_data['english'].strip()
            
            # اگر کلمه خالی است، آن را نشان می‌دهیم
            if not arabic_word:
                words_list.append(f"{i}. [خالی] (انگلیسی: {english_word})")
            else:
                words_list.append(f"{i}. {arabic_word} (انگلیسی: {english_word})")
        
        words_formatted = "\n".join(words_list)
        
        # پرامپت بهینه شده برای دسته‌های بزرگ‌تر
        batch_size = len(words_batch)

        prompt = f"""# مترجم تخصصی قرآن کریم

## نقش: مترجم حرفه‌ای متون قرآنی به زبان {target_language}

## آیه مرجع:
{verse_text}

## کلمات برای ترجمه ({batch_size} کلمه):
{words_formatted}

## دستورالعمل:
• ترجمه دقیق هر کلمه در زمینه آیه
• استفاده از واژگان مذهبی رایج {target_language}
• حفظ ترتیب دقیق کلمات (1 تا {batch_size})
• ترجمه‌های کوتاه و واضح
• در صورت کلمه خالی، خالی بگذارید

## خروجی JSON:
```json
{{"translations": ["ترجمه1", "ترجمه2", ..., "ترجمه{batch_size}"]}}
```

⚠️ فقط JSON برگردانید - بدون توضیح اضافی

# شروع ترجمه
"""
        
        return prompt.strip()
    
    @staticmethod
    def build_test_prompt() -> str:
        """پرامپت تست برای بررسی عملکرد API"""
        return """شما یک مترجم حرفه‌ای هستید. لطفاً کلمه "سلام" را به زبان آذربایجانی ترجمه کنید.

پاسخ را در قالب JSON زیر ارائه دهید:
{
    "translation": "ترجمه کلمه"
}

فقط JSON را برگردانید."""
    
    @staticmethod
    def build_validation_prompt(original_word: str, translation: str) -> str:
        """پرامپت اعتبارسنجی ترجمه"""
        return f"""لطفاً کیفیت ترجمه زیر را بررسی کنید:

کلمه عربی: {original_word}
ترجمه آذربایجانی: {translation}

آیا این ترجمه صحیح و مناسب است؟ پاسخ را در قالب JSON ارائه دهید:

{{
    "is_valid": true/false,
    "confidence": 0.0-1.0,
    "suggestion": "پیشنهاد بهتر (در صورت نیاز)"
}}"""
    
    @staticmethod
    def get_system_message() -> str:
        """پیام سیستم برای تنظیم رفتار مدل"""
        return """شما یک مترجم حرفه‌ای متون اسلامی و قرآنی هستید. وظیفه شما ترجمه دقیق و مناسب کلمات قرآن کریم است. همیشه:

1. از معنای دقیق و مناسب استفاده کنید
2. اصطلاحات مذهبی را رعایت کنید  
3. پاسخ‌های خود را در قالب JSON ارائه دهید
4. از توضیحات اضافی خودداری کنید
5. کیفیت و دقت را بر سرعت ترجیح دهید"""
