#!/usr/bin/env python
"""
Test script to verify the get_has_occasion_detail method works correctly with language filtering.
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from django.test import RequestFactory
from rest_framework.request import Request
from apps.najm_calendar.models import CalendarOccasions, OccasionDetail
from apps.najm_calendar.serializer import CalendarSerializer
from dj_language.models import Language

def test_occasion_detail_language_filtering():
    """Test that get_has_occasion_detail filters by language correctly."""
    
    print("🧪 Testing occasion detail language filtering...")
    
    # Create a mock request factory
    factory = RequestFactory()
    
    # Test different languages
    test_languages = ['fa', 'en', 'ar']
    
    # Find an occasion that has occasion details
    occasion_with_details = CalendarOccasions.objects.filter(
        occasion_details__isnull=False
    ).first()
    
    if not occasion_with_details:
        print("❌ No occasions with details found. Please create some test data first.")
        return
    
    print(f"📅 Testing with occasion: {occasion_with_details.title}")
    
    # Check which languages have occasion details for this occasion
    existing_details = OccasionDetail.objects.filter(occasion=occasion_with_details)
    existing_languages = set(detail.language.code for detail in existing_details if detail.language)
    
    print(f"📋 Existing occasion details languages: {existing_languages}")
    
    for language_code in test_languages:
        print(f"\n🌐 Testing language: {language_code}")
        
        # Create a mock request with the specific language
        django_request = factory.get('/', HTTP_ACCEPT_LANGUAGE=language_code)
        django_request.LANGUAGE_CODE = language_code
        request = Request(django_request)
        
        # Create serializer context
        context = {'request': request}
        
        # Test the serializer
        serializer = CalendarSerializer(occasion_with_details, context=context)
        has_detail = serializer.data['has_occasion_detail']
        
        # Expected result
        expected = language_code in existing_languages
        
        print(f"   📊 Has detail: {has_detail} (Expected: {expected})")
        
        if has_detail == expected:
            print(f"   ✅ Correct! Language filtering working properly")
        else:
            print(f"   ❌ Error! Expected {expected}, got {has_detail}")
    
    print("\n🎯 Test completed!")

if __name__ == "__main__":
    test_occasion_detail_language_filtering()
