from django.core.management.base import BaseCommand
from django.db import transaction
from apps.q_and_a.models import Consultants


class Command(BaseCommand):
    help = 'Check all consultants and set default values for session_duration, video_call_cost, and voice_call_cost if they are empty'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without actually making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # Default values
        default_session_duration = 20
        default_video_call_cost = 5
        default_voice_call_cost = 5
        
        # Get all consultants
        consultants = Consultants.objects.all()
        total_consultants = consultants.count()
        
        self.stdout.write(
            self.style.SUCCESS(f'Found {total_consultants} consultants to check')
        )
        
        updated_count = 0
        updates_made = []
        
        with transaction.atomic():
            for consultant in consultants:
                consultant_updates = []
                
                # Check session_duration
                if consultant.session_duration is None:
                    consultant_updates.append('session_duration')
                    if not dry_run:
                        consultant.session_duration = default_session_duration
                
                # Check video_call_cost
                if consultant.video_call_cost is None:
                    consultant_updates.append('video_call_cost')
                    if not dry_run:
                        consultant.video_call_cost = default_video_call_cost
                
                # Check voice_call_cost
                if consultant.voice_call_cost is None:
                    consultant_updates.append('voice_call_cost')
                    if not dry_run:
                        consultant.voice_call_cost = default_voice_call_cost
                
                # Save if there are updates
                if consultant_updates:
                    updated_count += 1
                    update_info = {
                        'id': consultant.id,
                        'username': consultant.username,
                        'fullname': consultant.fullname,
                        'fields': consultant_updates
                    }
                    updates_made.append(update_info)
                    
                    if not dry_run:
                        consultant.save(update_fields=consultant_updates)
                        self.stdout.write(
                            f'Updated consultant {consultant.username} (ID: {consultant.id}) - '
                            f'Fields: {", ".join(consultant_updates)}'
                        )
                    else:
                        self.stdout.write(
                            f'[DRY RUN] Would update consultant {consultant.username} (ID: {consultant.id}) - '
                            f'Fields: {", ".join(consultant_updates)}'
                        )
        
        # Summary
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'DRY RUN: {updated_count} consultants would be updated out of {total_consultants}')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully updated {updated_count} consultants out of {total_consultants}')
            )
        
        # Show detailed summary
        if updates_made:
            self.stdout.write('\n--- Summary of Updates ---')
            for update in updates_made:
                self.stdout.write(
                    f'• {update["username"]} ({update["fullname"]}) - ID: {update["id"]} - '
                    f'Fields: {", ".join(update["fields"])}'
                )
        else:
            self.stdout.write(
                self.style.SUCCESS('All consultants already have the required fields set!')
            )
        
        # Show default values used
        self.stdout.write('\n--- Default Values Applied ---')
        self.stdout.write(f'session_duration: {default_session_duration} minutes')
        self.stdout.write(f'video_call_cost: {default_video_call_cost}')
        self.stdout.write(f'voice_call_cost: {default_voice_call_cost}')
